!function(t){"use strict";t("#toastr-one5").on("click",function(i){t.NotificationApp.send("SUCCESS","测试修改信息成功~","top-right","rgba(0,0,0,0.2)","success")}),t("#toastr-two").on("click",function(i){t.NotificationApp.send("Heads up!","Check below fields please.","top-center","rgba(0,0,0,0.2)","warning")}),t("#toastr-three").on("click",function(i){t.NotificationApp.send("成功","信息修改成功~","rgba(0,0,0,0.2)","success")}),t("#toastr-four").on("click",function(i){t.NotificationApp.send("ERROR","修改信息失败!","top-right","rgba(0,0,0,0.2)","error")}),t("#toastr-five").on("click",function(i){t.NotificationApp.send("How to contribute?",["Fork the repository","Improve/extend the functionality","Create a pull request"],"top-right","rgba(0,0,0,0.2)","info")}),t("#toastr-six").on("click",function(i){t.NotificationApp.send("Can I add <em>icons</em>?","Yes! check this <a href='https://github.com/kamranahmedse/jquery-toast-plugin/commits/master'>update</a>.","top-right","rgba(0,0,0,0.2)","info",!1)}),t("#toastr-seven").on("click",function(i){t.NotificationApp.send("","Set the `hideAfter` property to false and the toast will become sticky.","top-right","rgba(0,0,0,0.2)","success")}),t("#toastr-eight").on("click",function(i){t.NotificationApp.send("","Set the `showHideTransition` property to fade|plain|slide to achieve different transitions.","top-right","rgba(0,0,0,0.2)","info",3e3,1,"fade")})}(window.jQuery);