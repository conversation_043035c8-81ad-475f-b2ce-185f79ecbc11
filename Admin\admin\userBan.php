<?php
session_start();
?>
<?php
include_once 'connect.php';
$ipchaxun = "select * from typecho_users";
$ipres = mysqli_query($connect, $ipchaxun);
$IPinfo = mysqli_fetch_array($ipres);
$uid = $_GET['uid'];

$ipres2 = mysqli_query($connect, "SELECT * FROM typecho_users WHERE uid = '$uid'");
$IPinfo2 = mysqli_fetch_array($ipres2);
if (!empty($IPinfo2['screenName'])) {
    $banusername = $IPinfo2['screenName'];
} else {
    $banusername = $IPinfo2['name'];
}
?>

<?php
include_once 'Nav.php';
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">用户封禁</h4>

                <form class="needs-validation" id="myForm" action="userBanPost.php" method="post" novalidate>
                    <div class="form-group mb-3">
                        <label for="validationCustom05">用户UID</label>
                        <input type="text" class="form-control" id="validationCustom05" placeholder="请输入封禁用户的UIP"
                               name="banuid" value="<?php echo $uid; ?>" readonly>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom06">用户名/昵称</label>
                        <input type="text" class="form-control" id="validationCustom06" value="<?php echo $banusername; ?>" readonly>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="validationCustom07">封禁原因</label>
                        <input type="text" class="form-control" id="validationCustom07"
                               placeholder="备注IP封禁原因" name="bantext" value="" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom08">封禁时间至</label>
                        <input type="datetime-local" class="form-control" id="validationCustom08"
                                name="bantime" value="<?php echo date('Y-m-d H:i:s'); ?>" required>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success"  type="submit" id="ipAddPost">立即封禁</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>

<?php
include_once 'Footer.php';
?>


<script>
    document.getElementById("myForm").addEventListener("submit", function(event) {
        event.preventDefault(); // 阻止表单默认提交行为

        var uid = document.getElementsByName("banuid")[0].value;
        var reason = document.getElementsByName("bantext")[0].value;
        var banTime = document.getElementsByName("bantime")[0].value;

        if (uid.trim() === "") {
            alert("请输入用户UID");
            return;
        }

        if (reason.trim() === "") {
            alert("请输入封禁原因");
            return;
        }

        if (banTime.trim() === "") {
            alert("请选择封禁时间");
            return;
        }
        var currentDate = new Date();
        var selectedDate = new Date(banTime);

        if (selectedDate <= currentDate) {
            alert("封禁时间必须大于当前时间");
            return;
        }
        var confirmSubmit = confirm("确定要封禁该用户吗？");

        if (confirmSubmit) {
            // 用户确认提交，可以提交表单
            this.submit();
        }
    });
</script>
</body>
</html>