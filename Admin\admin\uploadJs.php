function uploadFiles(uploadButton, uploadImage, picLinkInput, picLinkInputImg, logo) {
     
     uploadButton.addEventListener("click", function() {
         uploadImage.click();
     });

     uploadImage.addEventListener("change", function() {
         const selectedImages = uploadImage.files;
         const formData = new FormData();
         for (let i = 0; i < selectedImages.length; i++) {
             formData.append("file", selectedImages[i]);
         }

         fetch("<?php echo $API_UPLOAD_FULL.'?webkey='.$api_key; ?>", {
             method: 'POST',
             body: formData,
         })
         .then(response => response.json())
         .then(data => {
             if (data.errno == 0) {
                 picLinkInput.value = data.data.url;
                 if(picLinkInputImg!='null'){
                 picLinkInputImg.src = data.data.url;
                 }
                 if(logo!='null'){
                 logo.style.display = 'block';
                 }
                 toastr.success('上传成功', "提示"); 
             } else {
                 toastr.error(data.message, "上传失败 "); 
             }
         })
         .catch(error => {
             toastr.error(error, "上传失败"); 
         });
     });
 }