<?php
session_start();
?>

<?php


include_once 'connect.php';
$status = $_GET['status'];
$ids = $_GET['ids'];
$id = $_GET['id'];
$file = $_SERVER['PHP_SELF'];
if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    if ($status === 'Pass') {
        $sql = "UPDATE typecho_forum SET status = '1' WHERE id = '$id'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('通过成功');history.back();</script>";
        } else {
            echo "<script>alert('通过失败')';history.back();</script>";
        }
    } else if($status == 'selected'){
        $ids = isset($_GET['ids']) ? explode(',', $_GET['ids']) : array();  
      
        if (empty($ids)) {  
            echo "<script>alert('请至少选择一行进行删除。');history.back();</script>";  
            exit;  
        }  
      
        $placeholders = implode(',', array_fill(0, count($ids), '?'));  
        $sql = "DELETE FROM `typecho_forum` WHERE `id` IN ($placeholders)";  
      
        $stmt = $connect->prepare($sql);  
        if ($stmt === false) {  
            die('Prepare statement failed: ' . $connect->error);  
        }  
      
        $bindResult = $stmt->bind_param(str_repeat('i', count($ids)), ...$ids);  
        if ($bindResult === false) {  
            die('Binding parameters failed: ' . $stmt->error);  
        }  
      
        $executeResult = $stmt->execute();  
        if ($executeResult === true) {  
            $affectedRows = $stmt->affected_rows;  
            if($affectedRows==0){
                $affectedRows==1;
            }
            echo "<script>alert('删除成功，共删除了 {$affectedRows} 篇帖子');history.back();</script>";  
        } else {  
            echo "<script>alert('删除失败');history.back();</script>";  
        }  
    }else if($status == 'plPass'){  
        $ids = isset($_GET['ids']) ? explode(',', $_GET['ids']) : array();    
        
        if (empty($ids)) {    
            echo "<script>alert('请至少选择一行');history.back();</script>";    
            exit;    
        }    
        
        $placeholders = implode(',', array_fill(0, count($ids), '?'));  
        $sql = "UPDATE `typecho_forum` SET `status` = 1 WHERE `id` IN ($placeholders)";  
        
        $stmt = $connect->prepare($sql);  
        if ($stmt === false) {    
            die('Prepare statement failed: ' . $connect->error);    
        }    
        
        $bindResult = $stmt->bind_param(str_repeat('i', count($ids)), ...$ids);    
        if ($bindResult === false) {    
            die('Binding parameters failed: ' . $stmt->error);    
        }    
        
        $executeResult = $stmt->execute();    
        if ($executeResult === true) {    
            $affectedRows = $stmt->affected_rows;    
            echo "<script>alert('操作成功，共通过了 {$affectedRows} 篇帖子');history.back();</script>";    
        } else {    
            echo "<script>alert('操作成功');history.back();</script>";    
        }    
    }else if($status == 'plRefuse'){  
        $ids = isset($_GET['ids']) ? explode(',', $_GET['ids']) : array();    
        
        if (empty($ids)) {    
            echo "<script>alert('请至少选择一行');history.back();</script>";    
            exit;    
        }    
        
        $placeholders = implode(',', array_fill(0, count($ids), '?'));  
        $sql = "UPDATE `typecho_forum` SET `status` = 0 WHERE `id` IN ($placeholders)";  
        
        $stmt = $connect->prepare($sql);  
        if ($stmt === false) {    
            die('Prepare statement failed: ' . $connect->error);    
        }    
        
        $bindResult = $stmt->bind_param(str_repeat('i', count($ids)), ...$ids);    
        if ($bindResult === false) {    
            die('Binding parameters failed: ' . $stmt->error);    
        }    
        
        $executeResult = $stmt->execute();    
        if ($executeResult === true) {    
            $affectedRows = $stmt->affected_rows;    
            echo "<script>alert('操作成功，共拒绝了 {$affectedRows} 篇帖子');history.back();</script>";    
        } else {    
            echo "<script>alert('操作成功');history.back();</script>";    
        }    
    } else if($status === 'Refuse'){
        $sql = "UPDATE typecho_forum SET status = '0' WHERE id = '$id'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('拒绝成功');history.back();</script>";
        } else {
            echo "<script>alert('拒绝失败');history.back();</script>";
        }
    } else if($status === 'Lock'){
        $sql = "UPDATE typecho_forum SET status = '2' WHERE id = '$id'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('锁定成功');history.back();</script>";
        } else {
            echo "<script>alert('锁定失败');history.back();</script>";
        }
    } else if($status === 'unLock'){
        $sql = "UPDATE typecho_forum SET status = '1' WHERE id = '$id'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('解锁成功');history.back();</script>";
        } else {
            echo "<script>alert('解锁失败');history.back();</script>";
        }
    } else if($status === 'isrecommend'){
        $sql = "UPDATE typecho_forum SET isrecommend = '1' WHERE id = '$id'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('设置成功');history.back();</script>";
        } else {
            echo "<script>alert('设置失败');history.back();</script>";
        }
    } else if($status === 'unrecommend'){
        $sql = "UPDATE typecho_forum SET isrecommend = '0' WHERE id = '$id'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('设置成功');history.back();</script>";
        } else {
            echo "<script>alert('设置失败');history.back();</script>";
        }
    } else if($status === 'isswiper'){
        $sql = "UPDATE typecho_forum SET isswiper = '1' WHERE id = '$id'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('设置成功');history.back();</script>";
        } else {
            echo "<script>alert('设置失败');history.back();</script>";
        }
    } else if($status === 'unswiper'){
        $sql = "UPDATE typecho_forum SET isswiper = '0' WHERE id = '$id'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('设置成功');history.back();</script>";
        } else {
            echo "<script>alert('设置失败');history.back();</script>";
        }
    } else if($status === 'istop'){
        $sql = "UPDATE typecho_forum SET isTop = '1' WHERE id = '$id'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('设置成功');history.back();</script>";
        } else {
            echo "<script>alert('设置失败');history.back();</script>";
        }
    } else if($status === 'untop'){
        $sql = "UPDATE typecho_forum SET isTop = '0' WHERE id = '$id'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('设置成功');history.back();</script>";
        } else {
            echo "<script>alert('设置失败');history.back();</script>";
        }
    } else if ($status === 'Del') {
        $sql = "DELETE FROM typecho_forum WHERE id = $id";
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('删除成功');history.back();</script>";
        } else {
            echo "<script>alert('删除失败')';history.back();</script>";
        }
    }  else {
        echo "<script>alert('参数错误');history.back();</script>";
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}