<?php
session_start();
?>

<?php
include_once 'connect.php';
$sql = "select * from typecho_space order by id desc";
$contents = mysqli_query($connect, $sql);
?>

<?php
include_once 'Nav.php';
?>

<link href="/admin/assets/css/vendor/dataTables.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/responsive.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/buttons.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/select.bootstrap4.css" rel="stylesheet" type="text/css"/>
<!-- third party css end -->


<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">动态管理<a class="fabu" href="spaceAdd.php">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded right_10">
                            <i class="dripicons-upload"></i> 发布动态
                        </button>
                    </a></h4>
                <table id="basic-textlong" class="table dt-responsive nowrap" width="100%">
                    <thead>
                    <tr>
                        <th>cid</th>
                        <th>内容</th>
                        <th>时间</th>
                        <th>作者</th>
                        <th>状态</th>
                        <th>类型</th>
                        <th style="width: 125px;">操作</th>
                    </tr>
                    </thead>

                    <tbody>
                    <?php
                    while ($articledata = mysqli_fetch_array($contents)) {
                        ?>
                        <tr>
                            <td><?php echo $articledata['id'] ?></td>
                            <td>
                                 <?php echo $articledata['text'] ?>
                            </td>
                            <td>
                                <small class="text-muted"><?php 
                                $timestamp = $articledata['created'];
                                $date = date("Y-m-d H:i:s", $timestamp);
                                echo $date?></small>
                                
                            </td>
                            <td><small class="text-muted">UID:<?php echo $articledata['uid'] ?></small></td>
                           <td>
                                <h3>
                                    <?php if ($articledata['status']== '1') { ?><span class="badge badge-success-lighten">已发布</span><?php } else if ($articledata['status']== '2') { ?><span class="badge badge-danger-lighten">已锁定</span><?php } else { ?><span class="badge badge-warning-lighten">待审核</span><?php }?>
                                </h3>
                            </td>
                            <td>
                                <small class="text-muted">
                               <?php if ($articledata['type']== '0') {
                                    echo '<span class="badge badge-info-lighten">图文</span>';
                                } else if($articledata['type']== '4') {
                                    echo '<span class="badge badge-info-lighten">视频</span>';
                                } else if($articledata['type']== '1') {
                                    echo '<span class="badge badge-info-lighten">转发</span>';
                                } else if($articledata['type']== '2') {
                                    echo '<span class="badge badge-info-lighten">转发</span>';
                                } else if($articledata['type']== '3') {
                                    echo '<span class="badge badge-info-lighten">评论</span>';
                                } else if($articledata['type']== '5') {
                                    echo '<span class="badge badge-info-lighten">商品</span>';
                                }
                                ?>
                                </td>
                                </small>
                            <td>
                                
                                <?php 
                                 if ($articledata['status']== '2') {
                                    
                                    echo '<a href="spaceAuditPost.php?id='.$articledata['id'].'&status=Pass">
                                            <button style="white-space: nowrap;" type="button"
                                                    class="btn btn-info btn-rounded">
                                                <i class="dripicons-lock-open"></i> 解锁
                                            </button>
                                            </a>';
                                } else if ($articledata['status']== '1') {
                                     echo '<a href="spaceAuditPost.php?id='.$articledata['id'].'&status=Lock">
                                            <button style="white-space: nowrap;" type="button"
                                                    class="btn btn-info btn-rounded">
                                                <i class="dripicons-lock"></i> 锁定
                                            </button>
                                            </a>';
                                    
                                } else {
                                    echo '<a href="spaceAudit.php?id='.$articledata['id'].'">
                                            <button style="white-space: nowrap;" type="button"
                                                    class="btn btn-warning btn-rounded">
                                                <i class="dripicons-inbox"></i> 审核
                                            </button>
                                            </a>';
                                }
                                if ($articledata['type']== '0'||$articledata['type']== '4'||$articledata['type']== '3') {
                                    
                                    echo '<a href="spaceEdit.php?id='.$articledata['id'].'">
                                <button style="white-space: nowrap;" type="button"
                                        class="btn btn-primary btn-rounded">
                                    <i class="dripicons-document-edit"></i> 编辑
                                </button>
                                </a>';
                                }
                                ?>
                                <a href="javascript:del(<?php echo $articledata['id']; ?>);">
                                    <button style="white-space: nowrap;" type="button"
                                            class="btn btn-danger btn-rounded">
                                        <i class="mdi mdi-delete-empty mr-1"></i>删除
                                    </button>
                                </a>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                </table>

            </div>  
        </div>  
    </div> 
</div>


<script>
    function del(id) {
        if (confirm('您确认要删除id为' + id + '的动态吗？')) {
            location.href = 'spaceAuditPost.php?id=' + id +'&status=Del';
        }
    }
</script>


<?php
include_once 'Footer.php';
?>

<!-- third party js -->
<script src="/admin/assets/js/vendor/jquery.dataTables.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.bootstrap4.js"></script>
<script src="/admin/assets/js/vendor/dataTables.responsive.min.js"></script>
<script src="/admin/assets/js/vendor/responsive.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.buttons.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.html5.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.flash.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.print.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.keyTable.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.select.min.js"></script>
<!-- third party js ends -->
<!-- demo app -->
<script src="/admin/assets/js/pages/demo.datatable-init.js"></script>
<!-- end demo js-->


</body>
</html>