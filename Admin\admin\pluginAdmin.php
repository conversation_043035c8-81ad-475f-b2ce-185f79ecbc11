<?php
session_start();
include_once 'connect.php';
include_once 'Nav.php';


if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] == 'save') {
    function sendCurlRequest($apiURL, $action) {
        global $api_key;
        $ch = curl_init();
        
        $data = http_build_query([
            'action' => $action,
            'webkey' => $api_key
        ]);
    
        $url = $apiURL . '?' . $data;
    
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error_msg = curl_error($ch);
            curl_close($ch);
            return ['success' => false, 'error' => $error_msg];
    }

        curl_close($ch);
    
        $response_data = json_decode($response, true);
    
        return json_decode($response, true);
    }


    function modifyPluginConfig($plugin, $key, $value) {
        $pluginDir = dirname(__DIR__) . '/Plugins/' . $plugin;
        $configPath = $pluginDir . '/config.ini';

        if (file_exists($configPath)) {
            $config = parse_ini_file($configPath, true);
            $config['plugin'][$key] = $value;

            $newContent = '';
            foreach ($config as $section => $data) {
                $newContent .= "[$section]\n";
                foreach ($data as $k => $v) {
                    $newContent .= "$k = \"$v\"\n";
                }
            }

            if (file_put_contents($configPath, $newContent)) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    $type = $_GET['type'];
    $plugin = $_GET['plugin'];
    $isFree = isset($_GET['isFree']) ? $_GET['isFree'] : '';
    $result = false;
    $log = '';
    $error = '';
    $debugResponse = '';

    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    $domainName = $_SERVER['HTTP_HOST'];
    $baseURL = $protocol . $domainName;

    $API_Load_PluginLoad = $baseURL . '/Plugins/'.$plugin.'/index.php';
    $API_Load_FreePluginLoad = $baseURL . '/Plugins/'.$plugin.'/index.php';

    if ($isFree == 'true') {
        $apiURL = $API_Load_FreePluginLoad;
    } else {
        $apiURL = $API_Load_PluginLoad;
    }

    switch ($type) {
        case 'install':
            $response = sendCurlRequest($apiURL, 'install');
            $debugResponse = json_encode($response); 
            if ($response['code'] == 200) {
                $result = true;
                $log = $response['data']['log'];
            } else {
                $error = $response['msg'];
            }
            break;
        case 'uninstall':
            $response = sendCurlRequest($apiURL, 'uninstall');
            $debugResponse = json_encode($response); 
            if ($response['code'] == 200) {
                $result = true;
                $log = $response['data']['log'];
            } else {
                $error = $response['msg'];
            }
            break;
        case 'enable':
            $result = modifyPluginConfig($plugin, 'enabled', 'true');
            if (!$result) {
                $error = 'Failed to enable the plugin.';
            }
            break;
        case 'disable':
            $result = modifyPluginConfig($plugin, 'enabled', 'false');
            if (!$result) {
                $error = 'Failed to disable the plugin.';
            }
            break;
    }

   

    if ($result) {
        $redis = new Redis();
        if ($redis_password) {
            $redis->connect($redis_host, $redis_port);
            $redis->auth($redis_password);
        } else {
            $redis->connect($redis_host, $redis_port);
        }
        $redis->del('StarGetPlugin');
        if ($type == 'install' || $type == 'uninstall') {
            echo "<script>alert('操作成功，日志: $log');history.back();</script>";
        } else {
            echo "<script>alert('操作成功');history.back();</script>";
        }
    } else {
        echo "<script>alert('操作失败. Error: $error. Response: $debugResponse. url:$apiURL?action=install&webkey=$api_key');history.back();</script>";
    }
    exit;
}

$pluginDir = dirname(__DIR__) . '/Plugins';

$plugins = array();

if ($handle = opendir($pluginDir)) {
    while (false !== ($entry = readdir($handle))) {
        if ($entry != "." && $entry != "..") {
            $configPath = $pluginDir . '/' . $entry . '/config.ini';
            if (file_exists($configPath)) {
                $pluginInfo = parse_ini_file($configPath, true);
                $plugins[] = array(
                    'filename' => $entry,
                    'name' => $pluginInfo['plugin']['name'],
                    'author' => $pluginInfo['plugin']['author'],
                    'version' => $pluginInfo['plugin']['version'],
                    'enabled' => $pluginInfo['plugin']['enabled'],
                    'installed' => $pluginInfo['plugin']['installed'],
                    'isFree' => $pluginInfo['plugin']['isFree']
                );
            }
        }
    }
    closedir($handle);
}


?>

<link href="/admin/assets/css/vendor/dataTables.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/responsive.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/buttons.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/select.bootstrap4.css" rel="stylesheet" type="text/css"/>
<!-- third party css end -->

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">插件管理</h4>
                <table id="basic-chat" class="table dt-responsive nowrap" width="100%">
                    <thead>
                    <tr>
                        <th>名称</th>
                        <th>文件名</th>
                        <th>作者</th>
                        <th>版本</th>
                        <th>状态</th>
                        <th>安装</th>
                        <th style="width: 125px;">操作</th>
                    </tr>
                    </thead>

                    <tbody>
                    <?php foreach ($plugins as $plugin) { ?>
                        <tr>
                            <td><?php echo htmlspecialchars($plugin['name']); ?></td>
                            <td><?php echo htmlspecialchars($plugin['filename']); ?></td>
                            <td><?php echo htmlspecialchars($plugin['author']); ?></td>
                            <td><?php echo htmlspecialchars($plugin['version']); ?></td>
                            <td>
                                <h5>
                                    <?php if ($plugin['enabled']== 'true') { ?><span class="badge badge-success-lighten">已打开</span><?php } else { ?><span class="badge badge-danger-lighten">已关闭</span><?php } ?>
                                </h5>
                            <td>
                                <h5>
                                     <?php if ($plugin['installed']== 'true') { ?><span class="badge badge-success-lighten">已安装</span><?php } else { ?><span class="badge badge-danger-lighten">未安装</span><?php } ?>
                                </h5>
                            </td>
                            <td>
                                <?php if ($plugin['enabled'] == 'false') { ?>
                                    <a href="?action=save&type=enable&plugin=<?php echo $plugin['filename']; ?>" onclick="return confirm('确定要打开这个插件吗？')">
                                        <button style="white-space: nowrap;" type="button" class="btn btn-info btn-rounded">
                                            <i class="dripicons-checkmark"></i> 打开
                                        </button>
                                    </a>
                                <?php } elseif ($plugin['enabled'] == 'true' && $plugin['installed'] == 'false') { ?>
                                    <a href="?action=save&type=disable&plugin=<?php echo $plugin['filename']; ?>" onclick="return confirm('确定要关闭这个插件吗？')">
                                        <button style="white-space: nowrap;" type="button" class="btn btn-danger btn-rounded">
                                            <i class="dripicons-cross"></i> 关闭
                                        </button>
                                    </a>
                                    <a href="?action=save&type=install&plugin=<?php echo $plugin['filename']; ?>&isFree=<?php echo $plugin['isFree']; ?>" onclick="return confirm('确定要执行安装吗？安装会数据库造成修改，请在安装前备份好数据！')">
                                        <button style="white-space: nowrap;" type="button" class="btn btn-info btn-rounded">
                                            <i class="dripicons-inbox"></i> 安装
                                        </button>
                                    </a>
                                <?php } elseif ($plugin['enabled'] == 'true' && $plugin['installed'] == 'true') { ?>
                                    <a href="?action=save&type=uninstall&plugin=<?php echo $plugin['filename']; ?>&isFree=<?php echo $plugin['isFree']; ?>" onclick="return confirm('确定要执行卸载吗？卸载会删除插件相关的一切数据，请在卸载前备份好数据！')">
                                        <button style="white-space: nowrap;" type="button" class="btn btn-danger btn-rounded">
                                            <i class="dripicons-trash"></i> 卸载
                                        </button>
                                    </a>
                                <?php } ?>
                            </td>



                        </tr>
                    <?php } ?>
                    </tbody>
                </table>

            </div>
        </div>
    </div>
</div>

<?php
include_once 'Footer.php';
?>

<!-- third party js -->
<script src="/admin/assets/js/vendor/jquery.dataTables.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.bootstrap4.js"></script>
<script src="/admin/assets/js/vendor/dataTables.responsive.min.js"></script>
<script src="/admin/assets/js/vendor/responsive.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.buttons.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.html5.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.flash.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.print.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.keyTable.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.select.min.js"></script>
<!-- third party js ends -->
<!-- demo app -->
<script src="/admin/assets/js/pages/demo.datatable-init.js"></script>
<!-- end demo js-->

</body>
</html>