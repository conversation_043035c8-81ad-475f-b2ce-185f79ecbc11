<?php
session_start();
?>
<?php
include_once 'connect.php';
$url = $_POST['url'];
$name = $_POST['name'];
$link = $_POST['link'];
$lgof = $_POST['lgof'];
if (empty($url)) {
        $link = NULL;
        echo "<script>alert('请先上传图标');location.href = 'iconAdd.php';</script>";  
        die;
}

$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $charu = "insert into Sy_icon (url, name, link, lgof) values ('$url','$name','$link','$lgof')";
    $result = mysqli_query($connect, $charu);
    if ($result) {
            echo "<script>alert('添加成功');location.href = 'iconAdmin.php';</script>";
        } else {
            echo "<script>alert('添加失败');location.href = 'iconAdmin.php';</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
