<?php
session_start();
?>
<?php
include_once 'connect.php';
$version = $_POST['version'];
$versionCode = $_POST['versionCode'];
$versionIntro = $_POST['versionIntro'];
$versionUrl = $_POST['versionUrl'];
$force = $_POST['force'];

$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $charu = "insert into Sy_update (`version`, `versionCode`, `versionIntro`, `versionUrl`, `force`) values ('$version',$versionCode,'$versionIntro','$versionUrl',$force)";
    $result = mysqli_query($connect, $charu);
    if ($result) {
            echo "<script>alert('添加成功');location.href = 'updateAdmin.php';</script>";
        } else {
            echo "<script>alert('添加失败');location.href = 'updateAdmin.php';</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
