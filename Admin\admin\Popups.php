<?php
session_start();
?>



<?php
include_once 'Nav.php';
$sql = "SELECT * FROM Sy_popups";
$result = mysqli_query($connect, $sql);
// 检查查询结果是否为空
if (mysqli_num_rows($result) > 0) {
    // 获取第一行数据作为结果集
    $row = mysqli_fetch_assoc($result);
}
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">弹窗设置</h4>

                <form class="needs-validation" action="PopupsPost.php" method="post" onsubmit="return check()"
                      novalidate>
                    <div class="form-group mb-3" style="display:none;">
                      <label for="notice">发帖页弹窗：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="Postpopup" rows="6"><?php echo $row['Postpopup']; ?></textarea>
                    </div>
                    <div class="form-group mb-3" style="display:none;">
                      <label for="notice">商品发布规范：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="Shoppopup" rows="6"><?php echo $row['Shoppopup']; ?></textarea>
                    </div>
                     <div class="form-group mb-3">
                      <label for="notice">设置页页脚内容：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html 可放置备案信息</span>
                      <textarea id="notice" class="form-control" name="settext" rows="6"><?php echo $row['settext']; ?></textarea>
                    </div>
                    <div class="form-group mb-3">
                      <label for="notice">任务页说明：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="Taskpopup" rows="6"><?php echo $row['Taskpopup']; ?></textarea>
                    </div>
                    <div class="form-group mb-3">
                      <label for="notice">签到页说明：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="Signpopup" rows="6"><?php echo $row['Signpopup']; ?></textarea>
                    </div>
                    <div class="form-group mb-3">
                      <label for="notice">支付宝充值说明：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="Alipaypopup" rows="6"><?php echo $row['Alipaypopup']; ?></textarea>
                    </div>
                    <div class="form-group mb-3">
                      <label for="notice">微信充值说明：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="Wechatpopup" rows="6"><?php echo $row['Wechatpopup']; ?></textarea>
                    </div>
                    <div class="form-group mb-3">
                      <label for="notice">卡密充值说明：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="Camipopup" rows="6"><?php echo $row['Camipopup']; ?></textarea>
                    </div>
                    <div class="form-group mb-3">
                      <label for="notice">易支付充值说明：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="Yipaypopup" rows="6"><?php echo $row['Yipaypopup']; ?></textarea>
                    </div>
                    <div class="form-group mb-3">
                      <label for="notice">登录页弹窗：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="Loginpopup" rows="6"><?php echo $row['Loginpopup']; ?></textarea>
                    </div>
                    <div class="form-group mb-3">
                      <label for="notice">注册页弹窗：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="Registpopup" rows="6"><?php echo $row['Registpopup']; ?></textarea>
                    </div>
                    <div class="form-group mb-3">
                      <label for="notice">找回页弹窗：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="Forgetpopup" rows="6"><?php echo $row['Forgetpopup']; ?></textarea>
                    </div>
                    <div class="form-group mb-3">
                      <label for="notice">蓝V申请要求：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="lvtext" rows="6"><?php echo $row['lvtext']; ?></textarea>
                    </div>
                    <div class="form-group mb-3">
                      <label for="notice">实名认证说明：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="smtext" rows="6"><?php echo $row['smtext']; ?></textarea>
                    </div>
                     <div class="form-group mb-3">
                      <label for="notice">邀请好友说明：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="yqtext" rows="6"><?php echo $row['yqtext']; ?></textarea>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="PopupsPost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->

<script>
    function check() {
        // 获取所有文本域的值，并进行HTML编码
        var textareas = document.querySelectorAll("textarea");
        for (var i = 0; i < textareas.length; i++) {
            textareas[i].value = htmlEncode(textareas[i].value);
        }

        return true;
    }

    // 自定义HTML编码函数
    function htmlEncode(value) {
        return value.replace(/&/g, "&amp;")
                    .replace(/script/g, "?")
                    .replace(/SCRIPT/g, "?")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#39;");
    }
</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>