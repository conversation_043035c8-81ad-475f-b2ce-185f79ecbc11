<?php
session_start();
?>



<?php
include_once 'Nav.php';

//获取配置有问题
$curl = curl_init();
$url = $API_GET_API_CONFIG.'?webkey='.$api_key;
curl_setopt_array($curl, array(
   CURLOPT_URL => $url,
   CURLOPT_RETURNTRANSFER => true,
   CURLOPT_ENCODING => '',
   CURLOPT_MAXREDIRS => 10,
   CURLOPT_TIMEOUT => 0,
   CURLOPT_SSL_VERIFYPEER => false,
   CURLOPT_SSL_VERIFYHOST => false,
   CURLOPT_FOLLOWLOCATION => true,
   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
   CURLOPT_CUSTOMREQUEST => 'GET',
));

$response = curl_exec($curl);
$responseData = json_decode($response, true);  
  
if ($responseData && isset($responseData['code']) && $responseData['code'] == 1) {  
    $cmsSecretId = $responseData['data']['cmsSecretId'];    
    $cmsSecretKey = $responseData['data']['cmsSecretKey'];  
    $cmsRegion = $responseData['data']['cmsRegion'];  
    $cmsSwitch = $responseData['data']['cmsSwitch'];  
} 
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">内容安全</h4>
                <form class="needs-validation" action="contentViolationPost.php" method="post"
                      novalidate>
                     <div class="form-group mb-3">
                          <p>调用腾讯云内容安全检测接口，检测文本和图片是否存在违规内容，<a href="https://cloud.tencent.com/product/tms" target="_blank">官方文档</a></p>
                    </div>
                    <div class="form-group mb-3">
                          <label for="cmsSecretId">SecretId</label>
                          <input name="cmsSecretId" class="form-control" type="text" id="cmsSecretId" placeholder="请输入SecretId" value="<?php echo $cmsSecretId;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="cmsSecretKey">SecretKey</label>
                          <input name="cmsSecretKey" class="form-control" type="text" id="cmsSecretKey" placeholder="请输入SecretId" value="<?php echo $cmsSecretKey;  ?>">
                    </div>
                     <div class="form-group col-sm-4">
                    
                        <label for="validationCustom01">接口地域
                        <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              根据你的服务器地址做选择
                          </span></label>
                            <select class="form-control" id="example-select" name="cmsRegion">
                                <?php
                                $regions = [
                                    "ap-beijing" => "华北地区（北京）",
                                    "ap-guangzhou" => "华南地区（广州）",
                                    "ap-hongkong" => "港澳台地区（中国香港）",
                                    "ap-mumbai" => "亚太南部（孟买）",
                                    "ap-nanjing" => "华东地区（南京）",
                                    "ap-shanghai" => "华东地区（上海）",
                                    "ap-singapore" => "亚太东南（新加坡）",
                                    "ap-tokyo" => "亚太东北（东京）",
                                    "eu-frankfurt" => "欧洲地区（法兰克福）",
                                    "na-ashburn" => "美国东部（弗吉尼亚）",
                                    "na-siliconvalley" => "美国西部（硅谷）"
                                ];
                                
                                foreach ($regions as $key => $value) {
                                    $selected = ($cmsRegion == $key) ? "selected" : "";
                                    echo "<option value=\"$key\" $selected>$value</option>";
                                }
                                ?>
                            </select>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">内容安全等级
                        <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              处于开启状态时，会强制拦截内容或将其送入人工审核渠道
                          </span></label>
                        <select class="form-control" id="example-select" name="cmsSwitch">
                            <?php
                                $regions1 = [
                                    "0" => "关闭",
                                    "1" => "文本审核",
                                    "2" => "图片审核",
                                    "3" => "全部审核"
                                ];
                                foreach ($regions1 as $key1 => $value1) {
                                    $selected1 = ($cmsSwitch == $key1) ? "selected" : "";
                                    echo "<option value=\"$key1\" $selected1>$value1</option>";
                                }
                                ?>
                        </select>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="contentViolationPost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->




<?php
include_once 'Footer.php';
?>

</body>
</html>