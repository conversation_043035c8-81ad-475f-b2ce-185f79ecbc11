<?php
session_start();
?>

<?php
include_once 'connect.php';
$withdrawals = "SELECT * FROM typecho_userlog WHERE type = 'withdraw' ORDER BY id DESC";
$withdrawalsResult = mysqli_query($connect, $withdrawals);
?>

<?php
include_once 'Nav.php';
?>

<link href="/admin/assets/css/vendor/dataTables.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/responsive.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/buttons.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/select.bootstrap4.css" rel="stylesheet" type="text/css"/>
<!-- third party css end -->


<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">提现管理<a class="fabu" onclick="delAll()">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded right_10">
                            <i class="mdi mdi-delete-empty mr-1"></i> 清空
                        </button>
                    </a></h4>
                 
                <table id="basic-datatable" class="table dt-responsive nowrap" width="100%">
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>发起用户</th>
                        <th>时间</th>
                        <th>数额</th>
                        <th>状态</th>
                        <th style="width: 125px;">操作</th>
                    </tr>
                    </thead>

                    <tbody>
                    <?php
                    while ($withdrawal = mysqli_fetch_array($withdrawalsResult)) {
                        ?>
                        <tr>
                            <td><?php echo $withdrawal['id'] ?></td>
                            <td><?php echo $withdrawal['uid'] ?></td>
                            <td>
                                <small class="text-muted"><?php 
                                $timestamp = $withdrawal['created'];
                                $date = date("Y-m-d H:i:s", $timestamp);
                                echo $date?></small>
                                
                            </td>
                            <td><?php echo $withdrawal['num'] ?>积分</td>
                            <td>
                                <h5>
                                    <?php if ($withdrawal['cid']==-1) { ?><span class="badge badge-warning-lighten">待打款</span><?php } else if($withdrawal['cid']==-2) { ?><span class="badge badge-danger-lighten">已拒绝</span><?php } else { ?><span class="badge badge-success-lighten">已打款</span><?php } ?>
                                </h5>
                            </td>
                            <td>
                                <?php if ($withdrawal['cid']==-1) { ?><a href="payoutInfo.php?uid=<?php echo $withdrawal['uid']; ?>&id=<?php echo $withdrawal['id']; ?>&txje=<?php echo $withdrawal['num']; ?>">
                                    <button style="white-space: nowrap;" type="button"
                                            class="btn btn-info btn-rounded">
                                        <i class="dripicons-camera"></i> 审核
                                    </button>
                                </a><?php } ?>
                                <a href="javascript:del(<?php echo $withdrawal['id']; ?>);">
                                    <button style="white-space: nowrap;" type="button"
                                            class="btn btn-danger btn-rounded">
                                        <i class="mdi mdi-delete-empty mr-1"></i>删除
                                    </button>
                                </a>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                </table>

            </div>  
        </div>  
    </div> 
</div>


<script>
    function del(id) {
        if (confirm('您确认要删除该用户的提现申请吗？')) {
            location.href = 'payoutPost.php?id=' + id +'&status=Del';
        }
    }
    function delAll() {
        if (confirm('您确认要清空所有提现申请吗？')) {
            location.href = 'payoutPost.php?status=all';
        }
    }
</script>


<?php
include_once 'Footer.php';
?>

<!-- third party js -->
<script src="/admin/assets/js/vendor/jquery.dataTables.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.bootstrap4.js"></script>
<script src="/admin/assets/js/vendor/dataTables.responsive.min.js"></script>
<script src="/admin/assets/js/vendor/responsive.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.buttons.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.html5.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.flash.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.print.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.keyTable.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.select.min.js"></script>
<!-- third party js ends -->
<!-- demo app -->
<script src="/admin/assets/js/pages/demo.datatable-init.js"></script>
<!-- end demo js-->


</body>
</html>