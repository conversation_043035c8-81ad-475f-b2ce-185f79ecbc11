<?php
session_start();
?>

<?php

include_once 'connect.php';
$sql1 = "SELECT * FROM Sy_set";
$result = mysqli_query($connect, $sql1);
// 检查查询结果是否为空
if (mysqli_num_rows($result) > 0) {
    // 获取第一行数据作为结果集
    $row = mysqli_fetch_assoc($result);
}

$sql = "select * from typecho_shop WHERE type = 1 order by id desc";
$contents = mysqli_query($connect, $sql);
?>

<?php
include_once 'Nav.php';
?>

<link href="/admin/assets/css/vendor/dataTables.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/responsive.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/buttons.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/select.bootstrap4.css" rel="stylesheet" type="text/css"/>
<!-- third party css end -->


<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">商品管理</h4>
                <a class="fabu" href="shopTypeAdmin.php">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded left_10">
                             <i class="dripicons-tags"></i> 商品分类
                        </button>
                    </a>
                <a class="fabu" href="shopAdd.php">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded left_10">
                            <i class="dripicons-upload"></i> 发布商品
                        </button>
                    </a>
                 <br /><br />
                <table id="basic-shop" class="table dt-responsive nowrap" width="100%">
                    <thead>
                    <tr>
                        <th>id</th>
                        <th>标题</th>
                        <th>售价</th>
                        <th>时间</th>
                        <th>卖家</th>
                        <th>状态</th>
                        <th style="width: 125px;">操作</th>
                    </tr>
                    </thead>

                    <tbody>
                    <?php
                    while ($articledata = mysqli_fetch_array($contents)) {
                        ?>
                        <tr>
                            <td><small class="text-muted"><?php echo $articledata['id'] ?></small></td>
                            <td>
                                <?php echo $articledata['title'] ?>
                            </td>
                            
                            <td><?php echo $articledata['price'].$row['Assetname'] ?></td>
                            <td>
                                <?php 
                                $timestamp = $articledata['created'];
                                $date = date("Y-m-d H:i:s", $timestamp);
                                echo $date?>
                                
                            </td>
                            <td>uid:<?php echo $articledata['uid'] ?></td>
                            <td><h6>
                                    <?php if ($articledata['status']== '0') { ?><span class="badge badge-warning-lighten">待审核</span><?php } else  { ?><span class="badge badge-success-lighten">已通过</span><?php }  ?>
                                </h6></td>
                            <td>
                                <a href="shopInfo.php?id=<?php echo $articledata['id']; ?>">
                                <button style="white-space: nowrap;" type="button"
                                        class="btn btn-warning btn-rounded">
                                    <i class="dripicons-inbox"></i> 审核
                                </button>
                                </a>
                                <a href="shopEdit.php?id=<?php echo $articledata['id']; ?>">
                                <button style="white-space: nowrap;" type="button"
                                        class="btn btn-info btn-rounded">
                                    <i class="dripicons-document-edit"></i> 编辑
                                </button>
                                </a>



                                <a href="javascript:del(<?php echo $articledata['id']; ?>);">
                                    <button style="white-space: nowrap;" type="button"
                                            class="btn btn-danger btn-rounded">
                                        <i class="mdi mdi-delete-empty mr-1"></i>删除
                                    </button>
                                </a>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                </table>

            </div>  
        </div>  
    </div> 
</div>


<script>
    function del(id) {
        if (confirm('您确认要删除id为' + id + '的商品吗？')) {
            location.href = 'shopAuditPost.php?id=' + id +'&status=Del';
        }
    }
</script>


<?php
include_once 'Footer.php';
?>

<!-- third party js -->
<script src="/admin/assets/js/vendor/jquery.dataTables.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.bootstrap4.js"></script>
<script src="/admin/assets/js/vendor/dataTables.responsive.min.js"></script>
<script src="/admin/assets/js/vendor/responsive.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.buttons.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.html5.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.flash.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.print.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.keyTable.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.select.min.js"></script>
<!-- third party js ends -->
<!-- demo app -->
<script src="/admin/assets/js/pages/demo.datatable-init.js"></script>
<!-- end demo js-->


</body>
</html>