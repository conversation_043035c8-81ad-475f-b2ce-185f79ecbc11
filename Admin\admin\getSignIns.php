<?php
include_once 'connect.php';
    include 'common.php';
    if ($check_info == '1' || $check_info == '2' || $check_info == '3') {
        die("<script>alert('授权验证失败 错误码：0001');location.href = '../admin/login.php';</script>");
    } else if ($check_info == '0') {
        $title =  "授权验证成功";
        $msg = "StarDm正版授权";
        $zb = "1";
    } else {
        if ($domain !== $real_domain) {
        die("<script>alert('授权验证失败 错误码：0002');location.href = '../admin/login.php';</script>");
        }
        $title =  "授权验证成功";
        $msg = "StarDm正版授权";
        $zb = "1";
    }
$dataType = isset($_GET['dataType']) ? $_GET['dataType'] : '';

if ($dataType === 'signIns') {
    $signInsData = array();
    for ($i = 9; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-$i days"));
        $query = "SELECT COUNT(*) AS count FROM Sy_Signinlog WHERE DATE(time) = '$date'";
        $result = mysqli_query($connect, $query);
        $row = mysqli_fetch_assoc($result);
        $count = $row['count'];
        $signInsData[] = $count;
    }

    header('Content-Type: application/json');
    echo json_encode($signInsData);
}
if ($dataType === 'commentCount') {
    $totalCommentCountData = array();
    for ($i = 9; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-$i days"));
        $commentCountQuery = "SELECT COUNT(*) AS commentCount FROM typecho_comments WHERE DATE(FROM_UNIXTIME(created)) = '{$date}'";
        $commentResult = mysqli_query($connect, $commentCountQuery);
        $commentRow = mysqli_fetch_assoc($commentResult);
        $totalCommentCount = $commentRow['commentCount'];
        $totalCommentCountData[] = $totalCommentCount;
    }

    header('Content-Type: application/json');
    echo json_encode($totalCommentCountData);
}
if ($dataType === 'contents') {
    // 获取评论统计数据
    $totalcontentsData = array();
    for ($i = 9; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-$i days"));
        $contentsCountQuery = "SELECT COUNT(*) AS contents FROM typecho_contents WHERE DATE(FROM_UNIXTIME(created)) = '{$date}'";
        $contentsResult = mysqli_query($connect, $contentsCountQuery);
        $contentsRow = mysqli_fetch_assoc($contentsResult);
        $totalcontents = $contentsRow['contents'];
        $totalcontentsData[] = $totalcontents;
    }

    header('Content-Type: application/json');
    echo json_encode($totalcontentsData);
}
?>