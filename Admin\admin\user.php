<?php
session_start();
?>

<?php
include_once 'Nav.php';

?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">账号设置</h4>

                <form class="needs-validation" action="userPost.php" method="post" novalidate  onsubmit="return check()">
                    <div class="form-group mb-3">
                        <label for="validationCustom04">管理员账号</label>
                        <?php if ($login['user'] == $adminuser)  {?><span class="badge badge-danger-lighten"style="font-size: 0.8rem;">请尽快修改默认账号</span><?php }else{ ?> <span class="badge badge-success-lighten"style="font-size: 0.8rem;"></span> <?php } ?>
                        <input type="text" class="form-control"  placeholder="请输入新的管理员账号"
                               name="adminName" value="<?php echo $login['user'] ?>" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom05">管理员密码</label>
                        <?php if ($login['pw'] == md5($adminpw))  {?><span class="badge badge-danger-lighten"style="font-size: 0.8rem;"> 请尽快修改默认密码</span><?php }else{ ?> <span class="badge badge-success-lighten"style="font-size: 0.8rem;"></span> <?php } ?>
                        <input class="form-control"  name="pw" type="password" value="" placeholder="不修改请留空">
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success"  type="submit" id="userPost">提交修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  


</div>

<script>
    function check() {
       
        let adminName = document.getElementsByName('adminName')[0].value.trim();
        let pw = document.getElementsByName('pw')[0].value.trim();
        
        if (adminName.length == 0) {
            alert("请填写用户名");
            return false;
        }
        let user = /[a-zA-Z0-9]/g;
        let character = new RegExp("[`~!#$^&*()=|{}':;',\\[\\].<>/?~！#￥……&*（）——|{}【】‘；：”“'。，、？]");
        if (character.test(adminName)) {
            alert("用户名含有特殊字符 请重新修改")
            return false;
        }else if (!(user.test(adminName))) {
            alert("用户名只支持数字 英文大小写字母")
            return false;
        }
        
        if (character.test(pw)) {
            alert("密码含有特殊字符 请重新修改")
            return false;
        }
        
        

    }

</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>