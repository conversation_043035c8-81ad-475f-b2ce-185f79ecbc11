<?php
session_start();
$file = $_SERVER['PHP_SELF'];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    include_once 'connect.php';
    $Bannerswitch = isset($_POST['Bannerswitch']) ? $_POST['Bannerswitch'] : 0;
    $Bannernumber = $_POST['Bannernumber'];
    $Bannerimg1 = $_POST['Bannerimg1'];
    $Bannerurl1 = $_POST['Bannerurl1'];
    $Bannerimg2 = $_POST['Bannerimg2'];
    $Bannerurl2 = $_POST['Bannerurl2'];
    $Bannerimg3 = $_POST['Bannerimg3'];
    $Bannerurl3 = $_POST['Bannerurl3'];
    $Bannerimg4 = $_POST['Bannerimg4'];
    $Bannerurl4 = $_POST['Bannerurl4'];
    $Bannerimg5 = $_POST['Bannerimg5'];
    $Bannerurl5 = $_POST['Bannerurl5'];
    $Bannerimg6 = $_POST['Bannerimg6'];
    $Bannerurl6 = $_POST['Bannerurl6'];
    $Hyperlinks = isset($_POST['Hyperlinks']) ? $_POST['Hyperlinks'] : 0;
    $Gallery = isset($_POST['Gallery']) ? $_POST['Gallery'] : 0;
    $Findtop = isset($_POST['Findtop']) ? $_POST['Findtop'] : 0;
    $Code = isset($_POST['Code']) ? $_POST['Code'] : 0;
    
    if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
    $redisKeys = $connectRedis->keys('starapi_*');
        foreach ($redisKeys as $redisKey) {
            $connectRedis->del($redisKey);
        }
        // 写入数据库
        $query = "UPDATE Sy_pages SET Bannerswitch=?, Bannernumber=?, Bannerimg1=?, Bannerurl1=?, Bannerimg2=?, Bannerurl2=?, Bannerimg3=?, Bannerurl3=?, Bannerimg4=?, Bannerurl4=?, Bannerimg5=?, Bannerurl5=?, Bannerimg6=?, Bannerurl6=?, Hyperlinks=?, Gallery=?, Findtop=?, Code=?";
        $stmt = $connect->prepare($query);
        if ($stmt) {
            $stmt->bind_param("isssssssssssssiiii", $Bannerswitch, $Bannernumber, $Bannerimg1, $Bannerurl1, $Bannerimg2, $Bannerurl2, $Bannerimg3, $Bannerurl3, $Bannerimg4, $Bannerurl4, $Bannerimg5, $Bannerurl5, $Bannerimg6, $Bannerurl6, $Hyperlinks, $Gallery, $Findtop, $Code);

            if ($stmt->execute()) {
                echo "<script>alert('更改成功');location.href = 'findPage.php';</script>";
            } else {
                echo "<script>alert('更改失败');location.href = 'findPage.php';</script>";
            }
            $stmt->close();
        } else {
            echo "<script>alert('无法连接数据库');location.href = 'findPage.php';</script>";
        }
    } else {
        echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
    }
} else {
    
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
?>
