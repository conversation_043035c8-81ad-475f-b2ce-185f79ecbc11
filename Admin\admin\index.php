<?php
session_start();
?>

<?php
include_once 'connect.php';
// 签到统计
$today = date('Y-m-d');
$signCountQuery = "SELECT COUNT(*) AS signCount FROM Sy_Signinlog WHERE DATE(time) = '$today'";
$signResult = mysqli_query($connect, $signCountQuery);
$signRow = mysqli_fetch_assoc($signResult);
$totalSignCount = $signRow['signCount'];
// 格式化签到统计结果
if ($totalSignCount >= 1000 && $totalSignCount < 10000) {
    $totalSignCount = number_format($totalSignCount / 1000, 1) . 'k';
} elseif ($totalSignCount >= 10000) {
    $totalSignCount = number_format($totalSignCount / 10000, 1) . 'w';
}
// 评论统计
$commentCountQuery = "SELECT COUNT(*) AS commentCount FROM typecho_comments WHERE DATE(FROM_UNIXTIME(created)) = '{$today}'";
$commentResult = mysqli_query($connect, $commentCountQuery);
$commentRow = mysqli_fetch_assoc($commentResult);
$totalCommentCount = $commentRow['commentCount'];
// 格式化评论统计结果
if ($totalCommentCount >= 1000 && $totalCommentCount < 10000) {
    $totalCommentCount = number_format($totalCommentCount / 1000, 1) . 'k';
} elseif ($totalCommentCount >= 10000) {
    $totalCommentCount = number_format($totalCommentCount / 10000, 1) . 'w';
}
// 帖子统计
$contentsCountQuery = "SELECT COUNT(*) AS contents FROM typecho_contents WHERE DATE(FROM_UNIXTIME(created)) = '{$today}'";
$contentsResult = mysqli_query($connect, $contentsCountQuery);
$contentsRow = mysqli_fetch_assoc($contentsResult);
$totalcontents = $contentsRow['contents'];
// 格式化帖子统计结果
if ($totalcontents >= 1000 && $totalcontents < 10000) {
    $totalcontents = number_format($totalcontents / 1000, 1) . 'k';
} elseif ($totalcontents >= 10000) {
    $totalcontents = number_format($totalcontents / 10000, 1) . 'w';
}

$commentCountQuery1 = "SELECT COUNT(*) AS commentCountz FROM typecho_forum_comment WHERE DATE(FROM_UNIXTIME(created)) = '{$today}'";
$commentResult1 = mysqli_query($connect, $commentCountQuery1);
$commentRow1 = mysqli_fetch_assoc($commentResult1);
$totalCommentCount1 = $commentRow1['commentCountz'];
// 格式化评论统计结果
if ($totalCommentCount1 >= 1000 && $totalCommentCount1 < 10000) {
    $totalCommentCount1 = number_format($totalCommentCount1 / 1000, 1) . 'k';
} elseif ($totalCommentCount1 >= 10000) {
    $totalCommentCount1 = number_format($totalCommentCount1 / 10000, 1) . 'w';
}

$contentsCountQuery1 = "SELECT COUNT(*) AS contentsz FROM typecho_forum WHERE DATE(FROM_UNIXTIME(created)) = '{$today}'";
$contentsResult1 = mysqli_query($connect, $contentsCountQuery1);
$contentsRow1 = mysqli_fetch_assoc($contentsResult1);
$totalcontents1 = $contentsRow1['contentsz'];
// 格式化帖子统计结果
if ($totalcontents1 >= 1000 && $totalcontents1 < 10000) {
    $totalcontents1 = number_format($totalcontents1 / 1000, 1) . 'k';
} elseif ($totalcontents1 >= 10000) {
    $totalcontents1 = number_format($totalcontents1 / 10000, 1) . 'w';
}
?>
<?php
include_once 'Nav.php';
?>

    <div class="alert alert-success alert-dismissible bg-success text-white border-0 fade show" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
        <strong>StarPro</strong> - 关于本程序 <a style="color:white;" href="https://www.yuque.com/senyun-ev0j3/starpro/"><b>点击查看文档</b></a> 了解更多～
    </div>
<?php if ($login['user'] == $adminuser)  {?>
<div class="alert alert-danger alert-dismissible bg-danger text-white border-0 fade show" role="alert">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
    <strong>危险</strong> 当前账号为默认账号 请尽快修改！
</div>
<?php }?>

<?php if ($login['pw'] == md5($adminpw))  {?>
    <div class="alert alert-danger alert-dismissible bg-danger text-white border-0 fade show" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
        <strong>危险</strong> 当前密码为默认密码 请尽快修改！
    </div>
<?php }?>



<div class="row">

    <div class="col-md-6 col-xl-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-6">
                        <h5 class="text-muted font-weight-normal mt-0 text-truncate" title="Campaign Sent">今日签到</h5>
                        <h3 class="my-2 py-1"><?php echo $totalSignCount ?><i>人</i></h3>
                        
                    </div>
                    <div class="col-6">
                        <div class="text-right">
                            <div id="campaign-sent-chart"></div>
                        </div>
                    </div>
                </div> 
            </div>
        </div> 
    </div>

    <div class="col-md-6 col-xl-6">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-6">
                        <h5 class="text-muted font-weight-normal mt-0 text-truncate" title="New Leads">今日发帖</h5>
                        <h3 class="my-2 py-1"><?php echo $totalcontents1 ?><i>条</i></h3>
                    </div>
                    <div class="col-6">
                    </div>
                </div> 
            </div>
        </div> 
    </div> 

    <div class="col-md-6 col-xl-6">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-6">
                        <h5 class="text-muted font-weight-normal mt-0 text-truncate" title="Deals">帖子评论</h5>
                        <h3 class="my-2 py-1"><?php echo $totalCommentCount1 ?><i>条</i></h3>
                    </div>
                    <div class="col-6">
                    </div>
                </div> 
            </div> 
        </div> 
    </div> 



        <div class="col-md-6 col-xl-6">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-6">
                        <h5 class="text-muted font-weight-normal mt-0 text-truncate" title="Campaign Sent">今日发文</h5>
                        <h3 class="my-2 py-1"><?php echo $totalcontents ?><i>篇</i></h3>
                    </div>
                    <div class="col-6">
                    </div>
                </div> 
            </div>
        </div> 
    </div>

    <div class="col-md-6 col-xl-6">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-6">
                        <h5 class="text-muted font-weight-normal mt-0 text-truncate" title="New Leads">文章评论</h5>
                        <h3 class="my-2 py-1"><?php echo $totalCommentCount ?><i>条</i></h3>
                    </div>
                    <div class="col-6">
                    </div>
                </div> 
            </div>
        </div> 
    </div> 
</div>

<script src="/admin/assets/js/apexcharts.min.js"></script>

<script src="/admin/assets/js/ui/component.todo.js"></script>

<script src="/admin/assets/js/pages/demo.dashboard-crm.js"></script>
<?php
include_once 'Footer.php';
?>

</body>
</html>