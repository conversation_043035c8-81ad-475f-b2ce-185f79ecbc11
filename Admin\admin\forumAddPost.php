<?php
session_start();
?>
<?php
include_once 'connect.php';
$title = htmlspecialchars(trim($_POST['articletitle']),ENT_QUOTES);
$text = $_POST['articletext'];
$uid = trim($_POST['uid']);
$section = $_POST['section'];
$time = time();
$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $withdrawals2 = "SELECT parent FROM typecho_forum_section WHERE id = '$section'";
    $withdrawalsResult2 = mysqli_query($connect, $withdrawals2);
    $withdrawal2 = mysqli_fetch_array($withdrawalsResult2);
    $typeid = $withdrawal2['parent'];
    $charu = "insert into typecho_forum (title,created,modified,text,authorId,typeid,section,status) values ('$title','$time','$time','$text','$uid','$typeid','$section','1')";
    $result = mysqli_query($connect, $charu);
    if ($result) {
            echo "<script>alert('发布成功');location.href = 'forumAdmin.php';</script>";
        } else {
            echo "<script>alert('发布失败');location.href = 'forumAdmin.php';</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
