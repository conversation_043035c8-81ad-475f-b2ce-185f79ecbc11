<?php
session_start();
?>
<?php
include_once 'connect.php';
$id = trim($_POST['id']);
$uid = trim($_POST['uid']);
$type = trim($_POST['type']);
$pic = $_POST['pic'];
$text = $_POST['text'];
$only = isset($_POST['only']) ? $_POST['only'] : 0;
$category = trim($_POST['category']);
$time = time();
if (empty($pic)) {
    $pic = NULL;
}
if (empty($type)) {
    $type = '3';
}
$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    if ($type=='3') {
         $charu2 = "UPDATE typecho_space SET uid = '$uid' , modified = '$time', text = '$text' WHERE id = '$id'";
        $result2 = mysqli_query($connect, $charu2);
    
        if ($result2) {
            echo "<script>alert('修改成功');location.href = 'spaceAdmin.php';</script>";
        } else {
            echo "<script>alert('修改失败');location.href = 'spaceAdmin.php';</script>";   
        }
    }
    if ($type=='0'||$type=='4') {
        $charu = "UPDATE typecho_space SET uid = '$uid', modified = '$time', text = '$text', pic = '$pic', type = '$type', onlyMe = '$only' WHERE id = '$id'";
        $result = mysqli_query($connect, $charu);
        if ($result) {
                echo "<script>alert('修改成功');location.href = 'spaceAdmin.php';</script>";
            } else {
                echo "<script>alert('修改失败');location.href = 'spaceAdmin.php';</script>";   
            }
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
