<?php


?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <title>StarPro后台管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description"/>
    <meta content="Coderthemes" name="author"/>

    <!-- App css -->
    <link href="/admin/assets/css/icons.min.css" rel="stylesheet" type="text/css"/>
    <link href="/admin/assets/css/app.min.css" rel="stylesheet" type="text/css"/>
    <link href="/Style/css/loading.css" rel="stylesheet">
</head>

<script src="../Style/jquery/jquery.min.js"></script>


<style>
    .card {
        border-radius: 15px;
    }

    .card-header.pt-4.pb-4.text-center.bg-primary {
        border-radius: 15px 15px 0 0;
    }

    .btn-success {
        padding: 10px 25px;
        border-radius: 20px;
    }
    
    .anchor span {
        background: linear-gradient(45deg,#0dcda4,#c2fcd4);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: bold;
       
    }
    .btn-success {
        box-shadow: 0 2px 6px 0 #32b48f59;
        color: #fff;
        width: 100%;
        background-color: #50d5b0;
        border-color: #43c29e;
    }
    .btn-success:hover {
        box-shadow: 0 2px 6px 0 #0acf972b;
        color: #fff;
        width: 100%;
        background-color: #0acf97;
        border-color: #0acf97;
    }
    .custom-control-input:checked~.custom-control-label::before {
        color: #43c29e;
        border-color: #43c29e;
        background-color: #50d5b0;
    }
</style>

<body>

<div class="account-pages mt-5 mb-5" style="margin-top:7.5rem!important">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5">
                <div class="card">

                    <div class="card-body p-4">

                        <div class="text-center w-75 m-auto">
                            <h1>
                            <a class="anchor"><span>StarPro</span></a></h1>
                        </div>
                        <br>

                        <form action="loginPost.php" method="post" onsubmit="return check()">

                            <div class="form-group">
                                <label for="emailaddress">账号</label>
                                <input name="adminName" class="form-control" type="text" id="emailaddress" required=""
                                       placeholder="请输入管理员账号">
                            </div>

                            <div class="form-group">
                                <label for="password">密码</label>
                                <input name="pw" class="form-control" type="password" required="" id="password"
                                       placeholder="请输入密码">
                            </div>
                             <label for="captcha">验证码：</label>
                            <div class="form-inline" style="justify-content: space-between;flex-flow: initial;">
                                <div class="form-group" style="margin-bottom:0px;">
                                    <input name="captcha" class="form-control" style="padding: .45rem 0rem .45rem .9rem;" type="text" required="" id="captcha" placeholder="请输入验证码">
                                </div>
                                <img src="captcha.php" onclick="this.src='captcha.php'" alt="验证码">
                            </div>
                            <br>
                            <div class="form-group mb-3" style="text-align:right">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="checkbox-signin" checked>
                                    <label class="custom-control-label" for="checkbox-signin">记住密码</label>
                                </div>
                            </div>
                            

                            <div class="form-group mb-0 text-center">
                                <button class="btn btn-success" type="submit"> 立即登录</button>
                            </div>

                        </form>
                    </div> 
                </div>
             

            </div>
          

        </div> 
    </div>
   
</div>

</div>

<script>
    function check() {
       
        let adminName = document.getElementsByName('adminName')[0].value.trim();
        let pw = document.getElementsByName('pw')[0].value.trim();
        
        if (adminName.length == 0) {
            alert("请填写用户名");
            return false;
        } else if (pw.length == 0) {
            alert("请填写密码");
            return false;
        }
        let user = /[a-zA-Z0-9]/g;
        let character = new RegExp("[`~!#$^&*()=|{}':;',\\[\\].<>/?~！#￥……&*（）——|{}【】‘；：”“'。，、？]");
        if (character.test(adminName)) {
            alert("用户名含有特殊字符 请重新输入")
            return false;
        }else if (!(user.test(adminName))) {
            alert("用户名只支持数字 英文大小写字母")
            return false;
        }
        
        if (character.test(pw)) {
            alert("密码含有特殊字符 请重新输入")
            return false;
        }
        
        

    }

</script>

<footer class="footer footer-alt">
    Copyright © 2023 StarPro.
</footer>

<script src="/admin/assets/js/app.min.js"></script>
</body>
</html>
