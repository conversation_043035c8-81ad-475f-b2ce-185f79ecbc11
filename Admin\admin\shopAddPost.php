

<?php
session_start();
?>
<?php
include_once 'connect.php';
$num = $_POST['num'];
$subtype = $_POST['subtype'];
$uid = $_POST['uid'];
$title = $_POST['title'];
$price = $_POST['price'];
$vipDiscount = $_POST['vipDiscount'];
$imgurl = $_POST['imgurl'];
$text = $_POST['text'];
$value = $_POST['value'];
$file = $_SERVER['PHP_SELF'];
$time = time();

if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $article3 = "SELECT * FROM typecho_shoptype WHERE id='$subtype' limit 1";
    $resarticle3 = mysqli_query($connect, $article3);
    $mod3 = mysqli_fetch_array($resarticle3); //父级
    $sort = $mod3['parent'];
    $charu = "insert into typecho_shop (num,uid,title,price,vipDiscount,imgurl,text,value,created,status,subtype,sort) values ('$num','$uid','$title','$price','$vipDiscount','$imgurl','$text','$value','$time',1,'$subtype','$sort')";
    $result = mysqli_query($connect, $charu);
    if ($result) {
            echo "<script>alert('发布成功');location.href = 'shopAdmin.php';</script>";
        } else {
            echo "<script>alert('发布失败');location.href = 'shopAdmin.php';</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
