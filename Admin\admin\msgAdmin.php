<?php
session_start();
?>

<?php
include_once 'connect.php';
$id = $_GET['id'];
$type = $_GET['type'];
if ($type=='1') {
    $name = $_GET['name'];
} else {
    $uid = $_GET['uid'];
    $toid = $_GET['toid'];
}


$sql = "SELECT * FROM typecho_chat_msg WHERE cid = '$id' ORDER BY id DESC;";
$contents = mysqli_query($connect, $sql);
?>

<?php
include_once 'Nav.php';
?>

<link href="/admin/assets/css/vendor/dataTables.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/responsive.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/buttons.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/select.bootstrap4.css" rel="stylesheet" type="text/css"/>
<!-- third party css end -->


<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <?php if ($type=='1') { ?>
                    <h4 class="header-title mb-3">管理“<?php echo $name; ?>”群聊<a class="fabu" onclick="deleteAll1(<?php echo $id; ?>)">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded right_10">
                            <i class="mdi mdi-delete-empty mr-1"></i> 清空全部
                        </button>
                    </a></h4>
                <?php } else { ?>
                <h4 class="header-title mb-3">管理UID“<?php echo $uid; ?>与<?php echo $toid; ?>”的私聊<a class="fabu" onclick="deleteAll2(<?php echo $id; ?>)">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded right_10">
                            <i class="mdi mdi-delete-empty mr-1"></i> 清空全部
                        </button>
                    </a></h4>
                <?php } ?>
                
                <table id="basic-textlong" class="table dt-responsive nowrap" width="100%">
                    <thead>
                    <tr>
                        <th>id</th>
                        <th>内容</th>
                        <th>图片</th>
                        <th>发送人</th>
                        <th>时间</th>
                        <th>类型</th>
                        <th style="width: 125px;">操作</th>
                    </tr>
                    </thead>

                    <tbody>
                    <?php
                    while ($articledata = mysqli_fetch_array($contents)) {
                        ?>
                        <tr>
                            <td><?php echo $articledata['id'] ?></td>
                             <?php 
                             if ($articledata['text']== 'ban'&&$articledata['type']== '4') { ?>
                                <td>[管理员开启禁言]</td>
                                <td>
                                 
                                </td>
                             <?php  } 
                             if ($articledata['text']== 'noban'&&$articledata['type']== '4') { ?>
                            
                               <td> [管理员解除禁言]</td>
                               <td>
                                 
                                </td>
                             <?php } 
                             if ($articledata['type']== '0') { ?>
                             <td>
                                <?php echo $articledata['text'] ?>
                            </td>
                            <td>
                                 
                            </td>
                              <?php } 
                             if ($articledata['type']== '1'||$articledata['type']== '3') { ?>
                               <td>
                                 
                            </td>
                            <td>
                                
                                    <span class="dtr-data" id="yl"><img style="width: 100px;object-fit: cover;margin-top:20px;margin-right:20px;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="<?php echo $articledata['url'] ?>" class="spotlight"></span>
                            
                            </td>
                                <?php  } ?>
                             <td>
                                <small class="text-muted"><?php echo $articledata['uid'] ?></small>
                            </td>
                            <td>
                                <small class="text-muted"><?php 
                                $timestamp = $articledata['created'];
                                $date = date("Y-m-d H:i:s", $timestamp);
                                echo $date?></small>
                                
                            </td>
                           <td>
                                <h5>
                                    <?php if ($articledata['type']== '0') { ?>
                                    <span class="badge badge-success-lighten">文字</span>
                                    <?php } else if ($articledata['type']==  '1') { ?>
                                    <span class="badge badge-success-lighten">图片</span>
                                    <?php } else { ?>
                                    <span class="badge badge-info-lighten">系统</span>
                                    <?php }?>
                                </h5>
                            </td>
                            <td>
                                <a href="javascript:del(<?php echo $articledata['id']; ?>);">
                                    <button style="white-space: nowrap;" type="button"
                                            class="btn btn-danger btn-rounded">
                                        <i class="mdi mdi-delete-empty mr-1"></i>删除
                                    </button>
                                </a>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                </table>

            </div>  
        </div>  
    </div> 
</div>


<script>
    function del(id) {
        if (confirm('您确认要删除id为' + id + '的消息吗？')) {
            location.href = 'msgAdminPost.php?id=' + id +'&status=one';
        }
    }
    function deleteAll1(cid) {
        if (confirm('您确认要清空该群聊的所有消息吗？')) {
            location.href = 'msgAdminPost.php?cid=' + cid +'&status=all';
        }
    }
    function deleteAll2(cid) {
        if (confirm('您确认要清空该私聊的所有消息吗？')) {
            location.href = 'msgAdminPost.php?cid=' + cid +'&status=all';
        }
    }
</script>


<?php
include_once 'Footer.php';
?>

<!-- third party js -->
<script src="/admin/assets/js/vendor/jquery.dataTables.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.bootstrap4.js"></script>
<script src="/admin/assets/js/vendor/dataTables.responsive.min.js"></script>
<script src="/admin/assets/js/vendor/responsive.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.buttons.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.html5.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.flash.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.print.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.keyTable.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.select.min.js"></script>
<!-- third party js ends -->
<!-- demo app -->
<script src="/admin/assets/js/pages/demo.datatable-init.js"></script>
<!-- end demo js-->


</body>
</html>