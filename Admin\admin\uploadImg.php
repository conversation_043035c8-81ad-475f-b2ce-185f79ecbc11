<?php
session_start();
$time = time();
$file = $_SERVER['PHP_SELF'];
if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';  
    $domain = $_SERVER['HTTP_HOST']; 
    $allowedTypes = ['jpg', 'jpeg', 'gif', 'png', 'bmp', 'webp'];  
    $uploadDir = 'img/';  

    if (!is_dir($uploadDir)) {  
        mkdir($uploadDir, 0755, true);  
    }  

    if ($_FILES['editormd-image-file']['error'] === UPLOAD_ERR_OK) {  
        $file = $_FILES['editormd-image-file'];  
        $fileName = $file['name'];  
        $fileTmpPath = $file['tmp_name'];  
        $fileSize = $file['size'];  
        $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);  

        // 检查文件类型和大小  
        if (in_array($fileExtension, $allowedTypes) && $fileSize < 50 * 1024 * 1024) {  
            // 初始化 cURL
            $curl = curl_init();
            include_once 'connect.php';
            // 设置 cURL 选项
            $uploadUrl = $API_UPLOAD_FULL.'?webkey='.$api_key;
            $cFile = new CURLFile($fileTmpPath);
            $postFields = array('file' => $cFile);
            
            curl_setopt_array($curl, array(
                 CURLOPT_URL => $uploadUrl,
                 CURLOPT_RETURNTRANSFER => true,
                 CURLOPT_ENCODING => '',
                 CURLOPT_MAXREDIRS => 10,
                 CURLOPT_TIMEOUT => 0,
                 CURLOPT_FOLLOWLOCATION => true,
                 CURLOPT_SSL_VERIFYPEER => false,
                 CURLOPT_SSL_VERIFYHOST => false,
                 CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                 CURLOPT_CUSTOMREQUEST => 'POST',
                 CURLOPT_POSTFIELDS => $postFields,
            ));
            
            // 执行 cURL 请求
            $response = curl_exec($curl);
            
            // 关闭 cURL 资源
            curl_close($curl);
            
            // 检查是否上传成功
            $responseData = json_decode($response, true);
            if(isset($responseData['errno']) && $responseData['errno'] == 0){
                // 上传成功，返回图片链接
                $imageUrl = $responseData['data']['url'];
                
                // 返回 JSON 格式的响应  
                echo json_encode([  
                    'success' => 1,  
                    'message' => '上传成功',  
                    'url' => $imageUrl  
                ]);
            } else {
                $msg = $responseData['message'];
                // 返回上传失败信息
                echo json_encode([  
                    'success' => 0,  
                    'message' => $msg  
                ]);  
            }
        } else {  
            // 文件类型或大小不符合要求，返回错误信息  
            echo json_encode([  
                'success' => 0,  
                'message' => '文件类型或大小不符合要求'  
            ]);  
        }  
    } else {  
        // 文件上传错误，返回错误信息  
        echo json_encode([  
            'success' => 0,  
            'message' => '文件上传错误: ' . $_FILES['editormd-image-file']['error']  
        ]);  
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
?>