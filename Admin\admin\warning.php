<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8"/>
    <title>非法请求</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description"/>
    <meta content="Coderthemes" name="author"/>
    <link href="/admin/assets/css/icons.min.css" rel="stylesheet" type="text/css"/>
    <link href="/admin/assets/css/app.min.css" rel="stylesheet" type="text/css"/>
    <link href="/Style/css/loading.css" rel="stylesheet">
</head>
<script src="../Style/jquery/jquery.min.js"></script>


<?php
function get_ip_city($ip)
{
    $ch = curl_init();
    $url = 'https://whois.pconline.com.cn/ipJson.jsp?ip=' . $ip;
    //用curl发送接收数据
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //请求为https
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $location = curl_exec($ch);
    curl_close($ch);
    //转码
    $location = mb_convert_encoding($location, 'utf-8', 'GB2312');
    //var_dump($location);
    //截取{}中的字符串
    $location = substr($location, strlen('({') + strpos($location, '({'), (strlen($location) - strpos($location, '})')) * (-1));
    //将截取的字符串$location中的‘，’替换成‘&’   将字符串中的‘：‘替换成‘=’
    $location = str_replace('"', "", str_replace(":", "=", str_replace(",", "&", $location)));
    //php内置函数，将处理成类似于url参数的格式的字符串  转换成数组
    parse_str($location, $ip_location);
    return $ip_location['addr'];
}
$file = $_GET['route'];
include_once 'Database.php';
if ($file ){
    $gsd = get_ip_city($ip);
    if (empty($gsd)) {
        $gsd = '未知';
    }
    $ipcharu = "insert into Sy_warning (ip,gsd,time,file) values (?,?,?,?)";
    $stmt = $conn->prepare($ipcharu);
    $stmt->bind_param("ssss", $ip, $gsd, $time, $file);
    $ip = $_SERVER["REMOTE_ADDR"];
    
    $time = gmdate("Y-m-d H:i:s", time() + 8 * 3600);
    $file = $_GET['route'];
    $result = $stmt->execute();
    if (!$result) echo "错误信息：" . $stmt->error;
    $stmt->fetch();
}else{
    die ("参数错误");
}
?>

<style>
    .card {
        border-radius: 15px;
    }

    .card-header.pt-4.pb-4.text-center.bg-primary {
        border-radius: 15px 15px 0 0;
    }

    .btn-success {
        padding: 10px 25px;
        border-radius: 20px;
    }

    .info {
        margin: ;
        margin-bottom: 20px;
        font-size: 1.2rem;
    }
</style>

<body>

<div class="account-pages mt-5 mb-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5">
                <div class="card">
                    <div class="card-header pt-4 pb-4 text-center bg-primary">
                        <a href="#">
                                <span style="color: #fff;font-size: 1.3rem;font-family: '宋体';font-weight: 700;">非法请求</span>
                        </a>
                    </div>

                    <div class="card-body p-4">

                        <div class="text-center w-75 m-auto">
                            <div class="info">IP:<i style="color: #ff9b9b;"><?php echo $ip ?></i> 已记录到数据库</div>
                        </div>
                       

                    </div> 
                </div>


            </div>

        </div> 
    </div>
</div>
</div>
<footer class="footer">
    <div class="row footer_center">
        <div class="col-md-6">
            Copyright © 2023 Star Pro.
        </div>
    </div>
</footer>
<script>
    console.log("%c Star Pro v4.3.0 | Powered by SenYun", "color:#fff;background:linear-gradient(90deg, rgba(247,174,223,1) 0%, rgba(237,180,233,1) 24%, rgba(250,212,223,1) 94%);padding:8px 15px;border-radius:15px");
    console.log("%c QQ 2504531378", "color:#fff;background:#0acf97;padding:8px 15px;border-radius:15px");
    
</script>
<script src="/admin/assets/js/app.min.js"></script>
</body>

</html>