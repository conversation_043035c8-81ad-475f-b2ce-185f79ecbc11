<?php
session_start();
?>
<?php
include_once 'connect.php';
$uid = $_GET['uid'];
$id = $_GET['id'];
$txje = $_GET['txje'];
$ipres2 = mysqli_query($connect, "SELECT * FROM typecho_users WHERE uid = '$uid'");
$userdata = mysqli_fetch_array($ipres2);

if ($userdata['pay'] === NULL) {
    $dataNull = true;
    } else {
    $dataNull = false;
    $payData = explode("|", $userdata['pay']);
    $txWay = $payData[0];
    $txName = $payData[1];
    $txUser = $payData[2];
    $txImg = $payData[3];
    }

$query1 = "SELECT scale FROM typecho_apiconfig LIMIT 1"; // 假设只取第一个结果
$result1 = $connect->query($query1);

if ($result1 && $result1->num_rows > 0) {
    $row1 = $result1->fetch_assoc();
    $scale = $row1['scale'];
} else {
    $scale = null; // 处理没有结果的情况
}

// 查询Sy_set表中的Premium字段
$query2 = "SELECT Premium FROM Sy_set LIMIT 1"; // 假设只取第一个结果
$result2 = $connect->query($query2);

if ($result2 && $result2->num_rows > 0) {
    $row2 = $result2->fetch_assoc();
    $premium = $row2['Premium'];
} else {
    $premium = null; // 处理没有结果的情况
}

$txje = floatval($txje);
$scale = floatval($scale);
$premium = floatval($premium);
$actualAmount = $txje / $scale;

// 计算手续费
$fee = $actualAmount * ($premium / 100);

// 计算打款金额
$paymentAmount = $actualAmount - $fee;

?>

<?php
include_once 'Nav.php';
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">UID为<?php echo $uid ;?>的收款信息</h4>
                    
                <?php if($dataNull === false){
                    echo '<div class="form-group mb-3">
                        <label for="validationCustom05">收款方式：</label>
                        <input type="text" class="form-control" id="validationCustom05" value="'.$txWay.'" readonly>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom05">收款账号：</label> 
                        <input type="text" class="form-control" id="validationCustom05" value="'.$txUser.'" readonly>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom05">收款人：</label>
                        <input type="text" class="form-control" id="validationCustom05" value="'.$txName.'" readonly>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom05">提现详情：</label><br>
                        提现货币总额: ' . $txje . '<br>
                        货币的充值比例: ' . $scale . '<br>
                        提现手续费: ' . $premium . '<br>
                        实际打款金额: ' . $paymentAmount . '<br>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="yl" style="margin-top:.5rem">收款码：</label><br><span class="dtr-data" id="yl"><img style="width: 100%;object-fit: cover;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="'.$txImg.'" class="spotlight"></span></div>';
                } else {
                   echo '<div class="form-group mb-3">
                        <span class="badge badge-danger-lighten" style="font-size: 1.3rem;width:100%;height:200px;display: flex;justify-content: center;align-items: center;">该用户没有设置收款信息</span></div>';
                }
                ;?>
                    <br />
                    <div class="form-group mb-3 text_right">
                        <a class="fabu" onclick="Pass('<?php echo $id ;?>','<?php echo $txje ;?>','<?php echo $uid ;?>')">
                            <button class="btn btn-primary" id="payoutPost" style="margin-right:10px">已打款</button>
                        </a>
                        <a class="fabu" onclick="Refuse('<?php echo $id ;?>')">
                            <button class="btn btn-danger" id="payoutPost">拒绝</button>
                        </a>
                    </div>

            </div>  
        </div> 
    </div>  
</div>

<script>
    function Pass(id,je,uid) {
        if (confirm('您确认要通过该用户的提现吗？')) {
            location.href = 'payoutPost.php?id=' + id +'&txje='+ je +'&uid='+ uid +'&status=Pass';
        }
    }
    function Refuse(id) {
        if (confirm('您确认要拒绝该用户的提现吗？')) {
            location.href = 'payoutPost.php?id=' + id +'&status=Refuse';
        }
    }
    
</script>
<?php
include_once 'Footer.php';
?>

</body>
</html>