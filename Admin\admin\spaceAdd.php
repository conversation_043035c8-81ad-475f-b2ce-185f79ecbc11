<?php
session_start();
?>



<?php
include_once 'Nav.php';
$uploadUrl = $API_UPLOAD_FULL.'?webkey='.$api_key;
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">新增动态</h4>

                <form class="needs-validation" m action="spaceAddPost.php" method="post" onsubmit="return check()"
                      novalidate>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">发布者UID</label>
                        <input type="number" class="form-control" id="validationCustom01" placeholder="请输入发布者UID"
                               name="uid" required>
                    </div>
                    <div class="form-group col-sm-4">
                        <label>动态类型</label>
                            <select class="form-control" id="dynamic-type" name="type">
                                    <option value="pic" selected>图文动态</option>
                                    <option value="video">视频动态</option>
                            </select>
                    </div>
                    <div style="display:block;" id="pic">
                   <div class="form-group mb-3" id="validationCustom011">
                        <label>图片链接(选填) <button type="button" id="uploadButton" class="btn btn-success2 btn-sm btn-rounded right_10"><i class="dripicons-upload"></i> 上传图片</button></label>
                        <input type="text" class="form-control" id="picLinkInput" placeholder="图片链接" name="pic" readonly>
                        <input type="file" id="uploadImage" accept="image/*" style="display: none;" multiple>
                    </div>
                     </div>
                    <div style="display:block;" style="display:none;" id="video">
                        <div class="form-group mb-3" id="validationCustom012">
                            <label>视频链接 <button type="button" id="uploadmp4Button" class="btn btn-success2 btn-sm btn-rounded right_10"><i class="dripicons-upload"></i> 上传视频</button></label>
                            <input type="text" class="form-control" id="videoLinkInput" placeholder="视频链接" name="video" >
                            <input type="file" id="uploadMp4" accept="video/*" style="display:none;">
                        </div>
                    </div>
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler2(obj) {
                            var input = document.getElementById("switch1");
                            console.log(input);
                            if (obj.checked) {
                                console.log("仅自己可见打开");
                                input.value = "1";
                            } else {
                                console.log("仅自己可见关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">仅自己可见</label>
                    <input type="checkbox" name="only" id="switch1" value="0" data-switch="success"
                           onclick="myOnClickHandler2(this)">
                    <label id="switchurl" style="display:block;" for="switch1" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div>
                     <label for="validationCustom01">动态内容</label>
                        <textarea id="notice" class="form-control" rows="6" placeholder="输入动态内容" name="text" required></textarea>
                    
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-primary" type="submit" id="spaceAddPost">发布动态</button>
                    </div>
                </form>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<script>
    function check() {
        let uid = document.getElementsByName('uid')[0].value.trim();
        let text = document.getElementsByName('text')[0].value.trim();
        if (uid.length == 0) {
            alert("作者UID不能为空");
            return false;
        } else if (text.length == 0) {
            alert("动态内容不能为空");
            return false;
        }

    }
    // 获取动态类型的select元素
    var dynamicTypeSelect = document.getElementById("dynamic-type");
    
    // 获取id为1的div元素
    var div1 = document.getElementById("pic");
    
    // 获取id为2的div元素
    var div2 = document.getElementById("video");
    
    // 设置初始显示状态
    div1.style.display = "block";
    div2.style.display = "none";
    
    // 给动态类型select元素添加change事件监听器
    dynamicTypeSelect.addEventListener("change", function() {
      // 获取当前选中的值
      var selectedValue = dynamicTypeSelect.value;
      
      // 判断选中的值
      if (selectedValue === "pic") {
        // 显示id为1的div
        div1.style.display = "block";
        // 隐藏id为2的div
        div2.style.display = "none";
      } else if (selectedValue === "video") {
        // 隐藏id为1的div
        div1.style.display = "none";
        // 显示id为2的div
        div2.style.display = "block";
      }
    });
    // 获取上传图片按钮和文件上传输入框
    var uploadButton = document.getElementById("uploadButton");
    var uploadImage = document.getElementById("uploadImage");
    var picLinkInput = document.getElementById("picLinkInput");

    // 给上传图片按钮添加点击事件监听器
    
    uploadButton.addEventListener("click", function() {
        // 触发文件上传输入框的点击事件
        uploadImage.click();
    });
    
    // 给文件上传输入框添加change事件监听器
    uploadImage.addEventListener("change", function() {
        // 获取选择的图片文件
        const selectedImages = uploadImage.files;
    
        // 循环上传每张图片
        const promises = [];
        for (let i = 0; i < selectedImages.length; i++) {
            const formData = new FormData();
            formData.append("file", selectedImages[i]); // 添加当前图片文件到FormData对象
    
            // 发送POST请求上传图片
            const promise = fetch("<?php echo $uploadUrl; ?>", {
                method: 'POST',
                body: formData,
            })
            .then(response => response.json()) // 解析响应为 JSON 格式
            .then(data => {
                if (data.errno === 0) { // 检查 errno 来确定是否上传成功
                    console.log('图片上传成功');
                    return data.data.url; // 返回上传成功的图片链接
                } else {
                    throw new Error("上传失败：" + data.message); // 抛出错误
                }
            });
            
            promises.push(promise);
        }
    
        // 等待所有图片上传请求完成
        Promise.all(promises)
        .then(imageLinks => {
            // 将返回的图片链接连接起来并填写到图片链接输入框中
            const concatenatedLinks = imageLinks.join("||"); // 使用 "||" 连接多个图片链接
            picLinkInput.value = concatenatedLinks;
            toastr.success('图片上传成功', "提示"); 
        })
        .catch(error => {
            toastr.error(error, "上传失败 ");
            
        });
    });
    // 获取上传视频按钮和文件上传输入框
    var uploadmp4Button = document.getElementById("uploadmp4Button");
    var uploadVideo = document.getElementById("uploadMp4");
    var videoLinkInput = document.getElementById("videoLinkInput");
    
    // 给上传视频按钮添加点击事件监听器
    uploadmp4Button.addEventListener("click", function() {
        // 触发文件上传输入框的点击事件
        uploadVideo.click();
    });
    
    // 给文件上传输入框添加change事件监听器
    uploadVideo.addEventListener("change", function() {
        // 获取选择的视频文件
        var selectedVideo = uploadVideo.files[0];
    
        // 创建一个FormData对象
        var formData = new FormData();
    
        // 将选择的视频文件添加到FormData对象中
        formData.append("file", selectedVideo);
    
        // 发送POST请求上传视频
    
        var requestOptions = {
           method: 'POST',
           body: formData,
           redirect: 'follow'
        };
    
        fetch("<?php echo $uploadUrl; ?>", requestOptions)
           .then(response => response.json())
           .then(data => {
               if (data.errno === 0) {
                   // 上传成功，将返回的视频链接填写到视频链接输入框中
                   videoLinkInput.value = data.data.url;
                   toastr.success('视频上传成功', "提示"); 
               } else {
                   // 上传失败，处理错误消息
                   toastr.error("上传失败:", data.message);

               }
           })
           .catch(error => toastr.error('上传失败:', error));
    });
</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>