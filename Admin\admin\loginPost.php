<?php
error_reporting(0);
session_start();
// 获取用户输入的验证码
if (isset($_POST['captcha'])) {
    $captcha = strtolower(trim($_POST['captcha']));
} else {
    die("<script>alert('验证码未提供');location.href = '../admin/login.php';</script>");
}
// 获取 session 中的验证码
if (isset($_SESSION['captcha'])) {
    $captcha_session = strtolower(trim($_SESSION['captcha']));
} else {
    die("<script>alert('验证码未生成');location.href = '../admin/login.php';</script>");
}

//ip归属地
function get_ip_city($ip)
{
    $ch = curl_init();
    $url = 'https://whois.pconline.com.cn/ipJson.jsp?ip=' . $ip;
    //用curl发送接收数据
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //请求为https
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $location = curl_exec($ch);
    curl_close($ch);
    //转码
    $location = mb_convert_encoding($location, 'utf-8', 'GB2312');
    //var_dump($location);
    //截取{}中的字符串
    $location = substr($location, strlen('({') + strpos($location, '({'), (strlen($location) - strpos($location, '})')) * (-1));
    //将截取的字符串$location中的‘，’替换成‘&’   将字符串中的‘：‘替换成‘=’
    $location = str_replace('"', "", str_replace(":", "=", str_replace(",", "&", $location)));
    //php内置函数，将处理成类似于url参数的格式的字符串  转换成数组
    parse_str($location, $ip_location);
    return $ip_location['addr'];
}
if ($captcha === $captcha_session) {
    include_once "Database.php";
    
    $user = $_POST['adminName'];
    $pw = $_POST['pw'];

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $sql = "SELECT * FROM Sy_login WHERE user = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $user);
        $PW = md5($pw);
        $stmt->execute();
        $stmt->store_result();

        // 检查结果集是否有行
        if ($stmt->num_rows > 0) {
            $stmt->bind_result($id, $Login_user, $Login_pw);
            $stmt->fetch();
            if ($user === $Login_user && $PW === $Login_pw) {
                $_SESSION['loginadmin'] = $user;
                $ip = $_SERVER["REMOTE_ADDR"];
                // 获取 ip 地址归属地信息
                $gsd = get_ip_city($ip);
                if (empty($gsd)) {
                    $gsd = '未知';
                }
                $time = gmdate("Y-m-d H:i:s", time() + 8 * 3600);
                $stmt = $conn->prepare("INSERT INTO Sy_ip (ipAdd, Time, State) VALUES (?, ?, ?)");
                $stmt->bind_param("sss", $gsd, $time, $ip);
                $executeResult = $stmt->execute();
                if (!$executeResult) {
                    echo $stmt->error;
                } else {
                    echo "<script>alert('登录成功');location.href = '../admin/index.php';</script>";
                }
            } else {
                die("<script>alert('用户名或密码错误');location.href = '../admin/login.php';</script>");
            }
        } else {
            die("<script>alert('用户名或密码错误');location.href = '../admin/login.php';</script>");
        }
    }
} else {
   die("<script>alert('验证码错误');location.href = '../admin/login.php';</script>");
}
$conn->close();
?>
