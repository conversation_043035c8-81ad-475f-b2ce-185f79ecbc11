<?php
session_start();
?>



<?php
include_once 'Nav.php';

//获取配置有问题
$curl = curl_init();
$url = $API_GET_API_CONFIG.'?webkey='.$api_key;
curl_setopt_array($curl, array(
   CURLOPT_URL => $url,
   CURLOPT_RETURNTRANSFER => true,
   CURLOPT_ENCODING => '',
   CURLOPT_MAXREDIRS => 10,
   CURLOPT_TIMEOUT => 0,
   CURLOPT_SSL_VERIFYPEER => false,
   CURLOPT_SSL_VERIFYHOST => false,
   CURLOPT_FOLLOWLOCATION => true,
   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
   CURLOPT_CUSTOMREQUEST => 'GET',
));

$response = curl_exec($curl);
$responseData = json_decode($response, true);  
  
if ($responseData && isset($responseData['code']) && $responseData['code'] == 1) {  
    $postMax = $responseData['data']['postMax'];    
    $violationExp = $responseData['data']['violationExp'];  
    $deleteExp = $responseData['data']['deleteExp'];  
    $spaceMinExp = $responseData['data']['spaceMinExp'];  
    $chatMinExp = $responseData['data']['chatMinExp'];  
    $disableCode = $responseData['data']['disableCode'];  
    $allowDelete = $responseData['data']['allowDelete']; 
    $fields = $responseData['data']['fields'];  
    

} 
include_once 'connect.php';
$mb4url = $API_TO_UTF8MB4 . '?webkey=' . $api_key; 
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">其他发布设置</h4>

                <form class="needs-validation" action="setOtherPost.php" method="post"
                      novalidate>
                    
                     <div class="form-group mb-3">
                          <label for="postMax">每日最大发布数量
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              针对文章，动态，帖子等
                          </span></label>
                          <input name="postMax" class="form-control" type="number" required="" id="postMax" placeholder="请输入每日最大发布数量" value="<?php echo $postMax;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="violationExp">违规扣除经验
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              必须为整数，小于1则不扣除
                          </span></label>
                          <input name="violationExp" class="form-control" type="number" required="" id="violationExp" placeholder="请输入违规扣除经验" value="<?php echo $violationExp;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="deleteExp">删除扣除经验
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              删除文章，评论，动态，帖子时扣除，必须为整数，小于1则不扣除
                          </span></label>
                          <input name="deleteExp" class="form-control" type="number" required="" id="deleteExp" placeholder="请输入删除扣除经验" value="<?php echo $deleteExp;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="spaceMinExp">发布动态最低要求经验</label>
                          <input name="spaceMinExp" class="form-control" type="number" required="" id="spaceMinExp" placeholder="请输入发布动态最低要求经验" value="<?php echo $spaceMinExp;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="chatMinExp">聊天最低要求经验值</label>
                          <input name="chatMinExp" class="form-control" type="number" required="" id="chatMinExp" placeholder="请输入聊天最低要求经验值" value="<?php echo $chatMinExp;  ?>">
                    </div>
                    <div class="form-group col-sm-4">
                        <script>
                            function myOnClickHandler0(obj) {
                                var input = document.getElementById("disableCode");
                                console.log(input);
                                if (obj.checked) {
                                    input.setAttribute("value", "1");
                                } else {
                                    input.setAttribute("value", "0");
                                }
                            }
                        </script>
                        <label for="validationCustom08">敏感代码禁用模式
                         <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              开启后，则发布文章，商品，评论，都将禁用js或者嵌套代码
                          </span></label>
                        <?php
                        if ($disableCode=='1') {
                            echo '<input type="checkbox" name="disableCode" id="disableCode" value="1" data-switch="success"
                               onclick="myOnClickHandler0(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="disableCode" id="disableCode" value="0" data-switch="success"
                               onclick="myOnClickHandler0(this)">';
                        }
                        ?>
                        
                        <label id="switchurl" style="display:block;" for="disableCode" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group col-sm-4">
                        <script>
                            function myOnClickHandler5(obj) {
                                var input = document.getElementById("allowDelete");
                                console.log(input);
                                if (obj.checked) {
                                    input.setAttribute("value", "1");
                                } else {
                                    input.setAttribute("value", "0");
                                }
                            }
                        </script>
                        <label for="validationCustom08">用户删除权限<span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              允许用户删除自己的内容（评论，文章）
                          </span></label>
                         
                        <?php
                        if ($allowDelete=='1') {
                            echo '<input type="checkbox" name="allowDelete" id="allowDelete" value="1" data-switch="success"
                               onclick="myOnClickHandler5(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="allowDelete" id="allowDelete" value="0" data-switch="success"
                               onclick="myOnClickHandler5(this)">';
                        }
                        ?>
                        
                        <label id="switchurl" style="display:block;" for="allowDelete" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                          <label for="fields">文章自定义字段
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              输入字段的名称，根据英文逗号”,“进行分割。配置后，可通过自定义字段接口为文章设置自定义字段。
                          </span></label>
                          <input name="fields" class="form-control" type="text" required="" id="fields" placeholder="请输入文章自定义字段" value="<?php echo $fields;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="Utf8mb4">Utf8mb4支持
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              开启后，内容字段将变为Utf8mb4编码，支持emoji表情等特殊符号，但可能会占用更多的数据库空间。
                          </span></label>
                          
                    </div>
                    <div class="form-group col-sm-4">
                        <button class="btn btn-info" type="button" onclick="toMb4()">点击开启支持</button>
                    </div><br />
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="setOtherPost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->
<script>
    function toMb4() {
         var confirmed = confirm("确定要执行该操作吗？操作后所有内容字段开启Utf8mb4支持，此操作不可逆！");
        if (!confirmed) {
            return;
        }
        // 执行 AJAX 请求
        $.ajax({
            url: '<?php echo $mb4url;  ?>',
            type: 'GET',
            success: function(responseString) {
                var response = JSON.parse(responseString);
                // 根据响应中的 code 进行逻辑处理
                if (response.code === 1) {
                    // 如果请求成功且返回的 code 为 1，则显示成功消息
                    toastr.success(`操作成功`, "提示"); 
                } else if (response.code === 0) {
                    // 如果返回的 code 为 0，则显示 msg 内容
                    toastr.error(response.msg, "操作失败");
                } else {
                    // 如果返回的 code 不为 0 或 1，则显示默认错误消息
                    toastr.error(response, "提示");
                }
            },
            error: function() {
                // 请求失败时显示错误消息
                toastr.error(error, "提示");
            }
        });
    }
</script>


<?php
include_once 'Footer.php';
?>

</body>
</html>